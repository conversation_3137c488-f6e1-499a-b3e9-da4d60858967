import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class StraightThr<PERSON><PERSON><PERSON><PERSON><PERSON>(torch.autograd.Function):
    """Straight-through estimator for <PERSON><PERSON><PERSON> sampling."""
    
    @staticmethod
    def forward(ctx, probs):
        # Forward pass: hard sample (0 or 1)
        samples = torch.bernoulli(probs)
        return samples
    
    @staticmethod
    def backward(ctx, grad_output):
        # Backward pass: gradient flows through as if output was probs
        return grad_output


def straight_through_bernoulli(probs):
    """Apply straight-through <PERSON><PERSON><PERSON> sampling."""
    return StraightThroughBernoulli.apply(probs)


class Encoder(nn.Module):
    """Encoder network that outputs <PERSON><PERSON><PERSON> parameters."""
    
    def __init__(self, input_size=784, hidden_size=512, latent_size=64):
        super(Encoder, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, latent_size),
            nn.Sigmoid()  # Output <PERSON><PERSON> parameters in [0,1]
        )
        
    def forward(self, x):
        return self.network(x)


class Decoder(nn.Module):
    """Decoder network that reconstructs from latent codes."""
    
    def __init__(self, latent_size=64, hidden_size=512, output_size=784):
        super(Decoder, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(latent_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, output_size),
            nn.Sigmoid()  # Output probabilities for each pixel
        )
        
    def forward(self, z):
        return self.network(z)


class BernoulliVAE(nn.Module):
    """VAE with Bernoulli latents and straight-through estimator."""
    
    def __init__(self, input_size=784, hidden_size=512, latent_size=64):
        super(BernoulliVAE, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.latent_size = latent_size
        
        self.encoder = Encoder(input_size, hidden_size, latent_size)
        self.decoder = Decoder(latent_size, hidden_size, input_size)
        
    def encode(self, x):
        """Encode input to Bernoulli parameters."""
        return self.encoder(x)
    
    def sample_latent(self, q, use_straight_through=True):
        """Sample from Bernoulli distribution with optional straight-through."""
        if use_straight_through:
            return straight_through_bernoulli(q)
        else:
            return torch.bernoulli(q)
    
    def decode(self, z):
        """Decode latent codes to reconstruction."""
        return self.decoder(z)
    
    def forward(self, x, use_straight_through=True):
        """Full forward pass."""
        q = self.encode(x)
        z = self.sample_latent(q, use_straight_through)
        recon = self.decode(z)
        return recon, q, z


def bernoulli_kl_divergence(q, prior_prob=0.5):
    """
    Compute KL divergence between Bernoulli(q) and Bernoulli(prior_prob).
    
    KL(q || p) = q * log(q/p) + (1-q) * log((1-q)/(1-p))
    
    For prior_prob = 0.5:
    KL(q || 0.5) = q * log(2*q) + (1-q) * log(2*(1-q))
    """
    # Clamp to avoid log(0)
    q_clamped = torch.clamp(q, 1e-8, 1 - 1e-8)
    
    kl = (q_clamped * torch.log(2 * q_clamped) + 
          (1 - q_clamped) * torch.log(2 * (1 - q_clamped)))
    
    return torch.sum(kl, dim=1)  # Sum over latent dimensions


def bernoulli_entropy(q):
    """
    Compute entropy of Bernoulli distribution.
    H(q) = -q * log(q) - (1-q) * log(1-q)
    """
    # Clamp to avoid log(0)
    q_clamped = torch.clamp(q, 1e-8, 1 - 1e-8)
    
    entropy = -(q_clamped * torch.log(q_clamped) + 
                (1 - q_clamped) * torch.log(1 - q_clamped))
    
    return entropy


def reconstruction_loss(recon, target):
    """Binary cross-entropy reconstruction loss."""
    return F.binary_cross_entropy(recon, target, reduction='sum')


def estimate_total_correlation(z_samples, q_params, method='histogram'):
    """
    Estimate total correlation of discrete latent variables.
    
    TC = KL(q(z) || ∏_i q(z_i))
    
    For discrete variables, we can estimate this using empirical distributions.
    """
    batch_size, latent_dim = z_samples.shape
    
    if method == 'histogram':
        # Simple approximation: assume independence and compare
        # This is a placeholder - more sophisticated methods exist
        
        # Calculate marginal entropies
        marginal_entropies = []
        for i in range(latent_dim):
            # Empirical probability of z_i = 1
            p_i = torch.mean(z_samples[:, i])
            p_i = torch.clamp(p_i, 1e-8, 1 - 1e-8)
            
            # Entropy of this marginal
            h_i = -(p_i * torch.log(p_i) + (1 - p_i) * torch.log(1 - p_i))
            marginal_entropies.append(h_i)
        
        sum_marginal_entropies = torch.stack(marginal_entropies).sum()
        
        # Joint entropy (approximate using average entropy of q parameters)
        joint_entropy = torch.mean(torch.sum(bernoulli_entropy(q_params), dim=1))
        
        # TC = sum of marginal entropies - joint entropy
        tc = sum_marginal_entropies - joint_entropy
        
        return tc
    
    else:
        # Return zero if method not implemented
        return torch.tensor(0.0, device=z_samples.device)


def compute_vae_loss(recon, target, q, z, beta=1.0, gamma=0.0):
    """
    Compute total VAE loss.
    
    Args:
        recon: Reconstructed output
        target: Original input
        q: Bernoulli parameters from encoder
        z: Sampled latent codes
        beta: Weight for KL divergence
        gamma: Weight for total correlation
    """
    batch_size = target.size(0)
    
    # Reconstruction loss (BCE)
    recon_loss = reconstruction_loss(recon, target) / batch_size
    
    # KL divergence loss
    kl_loss = torch.mean(bernoulli_kl_divergence(q))
    
    # Total correlation loss
    if gamma > 0:
        tc_loss = estimate_total_correlation(z, q)
    else:
        tc_loss = torch.tensor(0.0, device=recon.device)
    
    # Total loss
    total_loss = recon_loss + beta * kl_loss + gamma * tc_loss
    
    return total_loss, recon_loss, kl_loss, tc_loss


if __name__ == "__main__":
    # Test the model
    print("Testing BernoulliVAE model...")
    
    # Create model
    model = BernoulliVAE(input_size=784, hidden_size=512, latent_size=64)
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    
    # Test forward pass
    batch_size = 32
    x = torch.randint(0, 2, (batch_size, 784)).float()  # Binary input
    
    print(f"Input shape: {x.shape}")
    print(f"Input range: [{x.min():.1f}, {x.max():.1f}]")
    
    # Forward pass
    recon, q, z = model(x)
    
    print(f"Reconstruction shape: {recon.shape}")
    print(f"Q parameters shape: {q.shape}")
    print(f"Latent samples shape: {z.shape}")
    print(f"Q range: [{q.min():.3f}, {q.max():.3f}]")
    print(f"Z values: {torch.unique(z)}")
    
    # Test loss computation
    total_loss, recon_loss, kl_loss, tc_loss = compute_vae_loss(
        recon, x, q, z, beta=1.0, gamma=0.1
    )
    
    print(f"\nLoss computation:")
    print(f"Total loss: {total_loss.item():.4f}")
    print(f"Reconstruction loss: {recon_loss.item():.4f}")
    print(f"KL loss: {kl_loss.item():.4f}")
    print(f"TC loss: {tc_loss.item():.4f}")
    
    # Test entropy calculation
    entropy = torch.mean(torch.sum(bernoulli_entropy(q), dim=1))
    print(f"Average latent entropy: {entropy.item():.4f} nats")
