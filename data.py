import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import struct


def read_idx_file(filename):
    """Read IDX file format used by MNIST dataset."""
    with open(filename, 'rb') as f:
        # Read magic number
        magic = struct.unpack('>I', f.read(4))[0]
        
        # Determine data type and number of dimensions
        # Magic number format: 0x00 0x00 data_type ndim
        data_type = (magic >> 8) & 0xFF
        ndim = magic & 0xFF
        
        # Read dimensions
        dims = []
        for _ in range(ndim):
            dims.append(struct.unpack('>I', f.read(4))[0])
        
        # Read data
        if data_type == 0x08:  # unsigned byte
            data = np.frombuffer(f.read(), dtype=np.uint8)
        elif data_type == 0x09:  # signed byte
            data = np.frombuffer(f.read(), dtype=np.int8)
        elif data_type == 0x0B:  # short (2 bytes)
            data = np.frombuffer(f.read(), dtype=np.int16)
        elif data_type == 0x0C:  # int (4 bytes)
            data = np.frombuffer(f.read(), dtype=np.int32)
        elif data_type == 0x0D:  # float (4 bytes)
            data = np.frombuffer(f.read(), dtype=np.float32)
        elif data_type == 0x0E:  # double (8 bytes)
            data = np.frombuffer(f.read(), dtype=np.float64)
        else:
            # For MNIST, we expect unsigned byte data (0x08)
            print(f"Warning: Unknown data type {data_type}, assuming unsigned byte")
            data = np.frombuffer(f.read(), dtype=np.uint8)
        
        # Reshape data
        data = data.reshape(dims)
        
    return data


def load_mnist_data():
    """Load MNIST dataset from IDX files."""
    train_images = read_idx_file('train-images.idx3-ubyte')
    train_labels = read_idx_file('train-labels.idx1-ubyte')
    test_images = read_idx_file('t10k-images.idx3-ubyte')
    test_labels = read_idx_file('t10k-labels.idx1-ubyte')
    
    return train_images, train_labels, test_images, test_labels


def preprocess_images(images, threshold=128):
    """Convert grayscale images to binary and normalize."""
    # Normalize to [0, 1]
    images = images.astype(np.float32) / 255.0
    
    # Convert to binary using threshold
    binary_images = (images > (threshold / 255.0)).astype(np.float32)
    
    # Flatten each image
    flattened = binary_images.reshape(binary_images.shape[0], -1)
    
    return flattened


class MNISTBinaryDataset(Dataset):
    """MNIST dataset with binary representation."""
    
    def __init__(self, images, labels, threshold=128):
        self.images = preprocess_images(images, threshold)
        self.labels = labels
        self.input_size = self.images.shape[1]  # 784
        
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.images[idx]), torch.LongTensor([self.labels[idx]])


def create_data_loaders(batch_size=128, threshold=128, shuffle_train=True):
    """Create data loaders for training and testing."""
    # Load MNIST data
    train_images, train_labels, test_images, test_labels = load_mnist_data()
    
    # Create datasets
    train_dataset = MNISTBinaryDataset(train_images, train_labels, threshold)
    test_dataset = MNISTBinaryDataset(test_images, test_labels, threshold)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=shuffle_train,
        num_workers=0,  # Avoid multiprocessing issues
        pin_memory=False
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        num_workers=0,
        pin_memory=False
    )
    
    return train_loader, test_loader, train_dataset.input_size


def calculate_dataset_entropy(data_loader, num_batches=None):
    """Calculate empirical entropy of the dataset."""
    all_data = []
    
    for i, (data, _) in enumerate(data_loader):
        if num_batches is not None and i >= num_batches:
            break
        all_data.append(data)
    
    # Concatenate all data
    all_data = torch.cat(all_data, dim=0).numpy()
    
    # Calculate empirical probabilities for each pixel
    pixel_probs = np.mean(all_data, axis=0)  # Probability of each pixel being 1
    
    # Calculate entropy for each pixel
    pixel_entropies = []
    for p in pixel_probs:
        if p == 0 or p == 1:
            entropy = 0  # No uncertainty
        else:
            entropy = -p * np.log2(p) - (1 - p) * np.log2(1 - p)
        pixel_entropies.append(entropy)
    
    # Total entropy (assuming independence)
    total_entropy = np.sum(pixel_entropies)
    
    return total_entropy, pixel_entropies


if __name__ == "__main__":
    # Test the data loader
    print("Testing data loader...")
    train_loader, test_loader, input_size = create_data_loaders(batch_size=64)
    
    print(f"Input size: {input_size}")
    print(f"Training batches: {len(train_loader)}")
    print(f"Test batches: {len(test_loader)}")
    
    # Test a batch
    for batch_idx, (data, labels) in enumerate(train_loader):
        print(f"Batch {batch_idx}: Data shape {data.shape}, Labels shape {labels.shape}")
        print(f"Data range: [{data.min():.3f}, {data.max():.3f}]")
        print(f"Data type: {data.dtype}")
        print(f"Unique values: {torch.unique(data)}")
        break
    
    # Calculate dataset entropy
    print("\nCalculating dataset entropy...")
    dataset_entropy, _ = calculate_dataset_entropy(test_loader, num_batches=10)
    print(f"Estimated dataset entropy: {dataset_entropy:.4f} bits")
    print(f"Estimated dataset entropy: {dataset_entropy * np.log(2):.4f} nats")
