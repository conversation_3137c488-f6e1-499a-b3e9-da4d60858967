import torch
import torch.optim as optim
import numpy as np
from tqdm import tqdm
import json
import os
from datetime import datetime

from data import create_data_loaders, calculate_dataset_entropy
from model import BernoulliVAE, compute_vae_loss, bernoulli_entropy


class VAETrainer:
    """Trainer for Bernoulli VAE with comprehensive logging."""
    
    def __init__(self, model, train_loader, test_loader, device='cpu'):
        self.model = model.to(device)
        self.train_loader = train_loader
        self.test_loader = test_loader
        self.device = device
        
        # Training history
        self.history = {
            'epoch': [],
            'train_total_loss': [],
            'train_recon_loss': [],
            'train_kl_loss': [],
            'train_tc_loss': [],
            'test_total_loss': [],
            'test_recon_loss': [],
            'test_kl_loss': [],
            'test_tc_loss': [],
            'latent_entropy_mean': [],
            'latent_entropy_std': [],
            'latent_sparsity': [],  # Fraction of latents close to 0 or 1
            'reconstruction_accuracy': []
        }
        
        # Configuration
        self.config = {
            'input_size': model.input_size,
            'hidden_size': model.hidden_size,
            'latent_size': model.latent_size,
            'total_params': sum(p.numel() for p in model.parameters())
        }
        
    def train_epoch(self, optimizer, beta=1.0, gamma=0.0):
        """Train for one epoch."""
        self.model.train()
        
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0
        total_tc_loss = 0
        
        all_q_params = []
        all_z_samples = []
        
        for batch_idx, (data, _) in enumerate(tqdm(self.train_loader, desc="Training")):
            data = data.to(self.device)
            
            optimizer.zero_grad()
            
            # Forward pass
            recon, q, z = self.model(data, use_straight_through=True)
            
            # Compute loss
            loss, recon_loss, kl_loss, tc_loss = compute_vae_loss(
                recon, data, q, z, beta=beta, gamma=gamma
            )
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            # Accumulate losses
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()
            total_tc_loss += tc_loss.item()
            
            # Collect samples for analysis
            all_q_params.append(q.detach().cpu())
            all_z_samples.append(z.detach().cpu())
        
        # Calculate averages
        num_batches = len(self.train_loader)
        avg_total_loss = total_loss / num_batches
        avg_recon_loss = total_recon_loss / num_batches
        avg_kl_loss = total_kl_loss / num_batches
        avg_tc_loss = total_tc_loss / num_batches
        
        # Analyze latent space
        all_q = torch.cat(all_q_params, dim=0)
        all_z = torch.cat(all_z_samples, dim=0)
        
        # Calculate entropy
        entropies = torch.sum(bernoulli_entropy(all_q), dim=1)
        avg_entropy = torch.mean(entropies).item()
        std_entropy = torch.std(entropies).item()
        
        # Calculate sparsity (fraction of q close to 0 or 1)
        sparsity = torch.mean(((all_q < 0.1) | (all_q > 0.9)).float()).item()
        
        return (avg_total_loss, avg_recon_loss, avg_kl_loss, avg_tc_loss,
                avg_entropy, std_entropy, sparsity)
    
    def test_epoch(self, beta=1.0, gamma=0.0):
        """Test for one epoch."""
        self.model.eval()
        
        total_loss = 0
        total_recon_loss = 0
        total_kl_loss = 0
        total_tc_loss = 0
        total_correct = 0
        total_pixels = 0
        
        with torch.no_grad():
            for data, _ in tqdm(self.test_loader, desc="Testing"):
                data = data.to(self.device)
                
                # Forward pass
                recon, q, z = self.model(data, use_straight_through=False)
                
                # Compute loss
                loss, recon_loss, kl_loss, tc_loss = compute_vae_loss(
                    recon, data, q, z, beta=beta, gamma=gamma
                )
                
                # Accumulate losses
                total_loss += loss.item()
                total_recon_loss += recon_loss.item()
                total_kl_loss += kl_loss.item()
                total_tc_loss += tc_loss.item()
                
                # Calculate reconstruction accuracy
                recon_binary = (recon > 0.5).float()
                correct = torch.sum(recon_binary == data).item()
                total_correct += correct
                total_pixels += data.numel()
        
        # Calculate averages
        num_batches = len(self.test_loader)
        avg_total_loss = total_loss / num_batches
        avg_recon_loss = total_recon_loss / num_batches
        avg_kl_loss = total_kl_loss / num_batches
        avg_tc_loss = total_tc_loss / num_batches
        accuracy = total_correct / total_pixels
        
        return avg_total_loss, avg_recon_loss, avg_kl_loss, avg_tc_loss, accuracy
    
    def train(self, num_epochs, lr=1e-3, beta=1.0, gamma=0.0, 
              save_every=10, results_dir='results'):
        """Train the VAE."""
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        
        # Setup optimizer
        optimizer = optim.Adam(self.model.parameters(), lr=lr)
        
        # Save configuration
        self.config.update({
            'num_epochs': num_epochs,
            'learning_rate': lr,
            'beta': beta,
            'gamma': gamma,
            'device': str(self.device)
        })
        
        print("=" * 60)
        print("BERNOULLI VAE TRAINING")
        print("=" * 60)
        print(f"Model: {self.config['total_params']:,} parameters")
        print(f"Latent size: {self.config['latent_size']}")
        print(f"Epochs: {num_epochs}")
        print(f"Learning rate: {lr}")
        print(f"Beta (KL weight): {beta}")
        print(f"Gamma (TC weight): {gamma}")
        print(f"Device: {self.device}")
        print("=" * 60)
        
        best_test_loss = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch + 1}/{num_epochs}")
            
            # Train
            train_results = self.train_epoch(optimizer, beta, gamma)
            (train_total, train_recon, train_kl, train_tc, 
             entropy_mean, entropy_std, sparsity) = train_results
            
            # Test
            test_results = self.test_epoch(beta, gamma)
            test_total, test_recon, test_kl, test_tc, accuracy = test_results
            
            # Record history
            self.history['epoch'].append(epoch + 1)
            self.history['train_total_loss'].append(train_total)
            self.history['train_recon_loss'].append(train_recon)
            self.history['train_kl_loss'].append(train_kl)
            self.history['train_tc_loss'].append(train_tc)
            self.history['test_total_loss'].append(test_total)
            self.history['test_recon_loss'].append(test_recon)
            self.history['test_kl_loss'].append(test_kl)
            self.history['test_tc_loss'].append(test_tc)
            self.history['latent_entropy_mean'].append(entropy_mean)
            self.history['latent_entropy_std'].append(entropy_std)
            self.history['latent_sparsity'].append(sparsity)
            self.history['reconstruction_accuracy'].append(accuracy)
            
            # Print progress
            print(f"Train - Total: {train_total:.4f}, Recon: {train_recon:.4f}, "
                  f"KL: {train_kl:.6f}, TC: {train_tc:.6f}")
            print(f"Test  - Total: {test_total:.4f}, Recon: {test_recon:.4f}, "
                  f"KL: {test_kl:.6f}, TC: {test_tc:.6f}")
            print(f"Latent Entropy: {entropy_mean:.4f} ± {entropy_std:.4f}")
            print(f"Sparsity: {sparsity:.3f}, Accuracy: {accuracy:.4f}")
            
            # Save best model
            if test_total < best_test_loss:
                best_test_loss = test_total
                self.save_checkpoint(os.path.join(results_dir, 'best_model.pth'))
            
            # Save periodic checkpoint
            if (epoch + 1) % save_every == 0:
                checkpoint_path = os.path.join(results_dir, f'checkpoint_epoch_{epoch+1}.pth')
                self.save_checkpoint(checkpoint_path)
        
        print("\n" + "=" * 60)
        print("TRAINING COMPLETED!")
        print(f"Best test loss: {best_test_loss:.4f}")
        print("=" * 60)
        
        # Save final results
        self.save_checkpoint(os.path.join(results_dir, 'final_model.pth'))
        self.save_history(os.path.join(results_dir, 'training_history.json'))
        
        return self.history
    
    def save_checkpoint(self, filepath):
        """Save model checkpoint."""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'history': self.history
        }, filepath)
    
    def save_history(self, filepath):
        """Save training history as JSON."""
        with open(filepath, 'w') as f:
            json.dump(self.history, f, indent=2)


def main():
    """Main training function."""
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load data
    print("Loading MNIST data...")
    train_loader, test_loader, input_size = create_data_loaders(
        batch_size=128, threshold=128, shuffle_train=True
    )
    
    # Calculate dataset entropy
    print("Calculating dataset entropy...")
    dataset_entropy_bits, _ = calculate_dataset_entropy(test_loader, num_batches=50)
    dataset_entropy_nats = dataset_entropy_bits * np.log(2)
    print(f"Dataset entropy: {dataset_entropy_bits:.2f} bits ({dataset_entropy_nats:.2f} nats)")
    
    # Create model
    latent_size = 64  # Much smaller than input for compression
    model = BernoulliVAE(
        input_size=input_size,
        hidden_size=512,
        latent_size=latent_size
    )
    
    # Create trainer
    trainer = VAETrainer(model, train_loader, test_loader, device)
    
    # Train model
    history = trainer.train(
        num_epochs=100,
        lr=1e-3,
        beta=1.0,      # Standard VAE
        gamma=0.0,     # No TC penalty initially
        save_every=20,
        results_dir='results_bernoulli'
    )
    
    return trainer, history


if __name__ == "__main__":
    trainer, history = main()
