import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from datetime import datetime

from data import create_data_loaders
from model import <PERSON><PERSON><PERSON>VAE


# Set matplotlib backend to avoid display issues
plt.switch_backend('Agg')


def load_model_and_history(checkpoint_path):
    """Load trained model and history."""
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    
    config = checkpoint['config']
    history = checkpoint['history']
    
    # Recreate model
    model = BernoulliVAE(
        input_size=config['input_size'],
        hidden_size=config['hidden_size'],
        latent_size=config['latent_size']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, history, config


def plot_loss_curves(history, save_path=None):
    """Plot comprehensive loss curves."""
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    epochs = history['epoch']
    
    # Total loss
    axes[0, 0].plot(epochs, history['train_total_loss'], 'b-', label='Train', linewidth=2)
    axes[0, 0].plot(epochs, history['test_total_loss'], 'r-', label='Test', linewidth=2)
    axes[0, 0].set_title('Total Loss', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Reconstruction loss
    axes[0, 1].plot(epochs, history['train_recon_loss'], 'b-', label='Train', linewidth=2)
    axes[0, 1].plot(epochs, history['test_recon_loss'], 'r-', label='Test', linewidth=2)
    axes[0, 1].set_title('Reconstruction Loss (BCE)', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # KL divergence
    axes[0, 2].plot(epochs, history['train_kl_loss'], 'b-', label='Train', linewidth=2)
    axes[0, 2].plot(epochs, history['test_kl_loss'], 'r-', label='Test', linewidth=2)
    axes[0, 2].set_title('KL Divergence Loss', fontsize=14, fontweight='bold')
    axes[0, 2].set_xlabel('Epoch')
    axes[0, 2].set_ylabel('Loss')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # Latent entropy
    axes[1, 0].plot(epochs, history['latent_entropy_mean'], 'g-', linewidth=2)
    axes[1, 0].fill_between(epochs, 
                           np.array(history['latent_entropy_mean']) - np.array(history['latent_entropy_std']),
                           np.array(history['latent_entropy_mean']) + np.array(history['latent_entropy_std']),
                           alpha=0.3, color='g')
    axes[1, 0].set_title('Latent Entropy Evolution', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Entropy (nats)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Sparsity
    axes[1, 1].plot(epochs, history['latent_sparsity'], 'purple', linewidth=2)
    axes[1, 1].set_title('Latent Sparsity', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Fraction near 0 or 1')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Reconstruction accuracy
    axes[1, 2].plot(epochs, history['reconstruction_accuracy'], 'orange', linewidth=2)
    axes[1, 2].set_title('Reconstruction Accuracy', fontsize=14, fontweight='bold')
    axes[1, 2].set_xlabel('Epoch')
    axes[1, 2].set_ylabel('Pixel Accuracy')
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Loss curves saved to {save_path}")
    
    plt.close()


def plot_reconstructions(model, data_loader, num_samples=10, save_path=None):
    """Visualize original vs reconstructed images."""
    model.eval()
    
    # Get a batch of data
    data, labels = next(iter(data_loader))
    
    with torch.no_grad():
        recon, q, z = model(data, use_straight_through=False)
    
    # Convert to numpy
    data_np = data.numpy()
    recon_np = recon.numpy()
    q_np = q.numpy()
    z_np = z.numpy()
    
    fig, axes = plt.subplots(4, num_samples, figsize=(num_samples * 2, 8))
    
    for i in range(num_samples):
        # Original
        axes[0, i].imshow(data_np[i].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
        axes[0, i].set_title(f'Original {labels[i].item()}', fontsize=10)
        axes[0, i].axis('off')
        
        # Reconstructed (continuous)
        axes[1, i].imshow(recon_np[i].reshape(28, 28), cmap='gray', vmin=0, vmax=1)
        axes[1, i].set_title('Recon (prob)', fontsize=10)
        axes[1, i].axis('off')
        
        # Reconstructed (binary)
        recon_binary = (recon_np[i] > 0.5).astype(float)
        axes[2, i].imshow(recon_binary.reshape(28, 28), cmap='gray', vmin=0, vmax=1)
        axes[2, i].set_title('Recon (binary)', fontsize=10)
        axes[2, i].axis('off')
        
        # Latent code (as grayscale bar)
        latent_img = np.tile(q_np[i], (4, 1))  # Repeat to make visible
        axes[3, i].imshow(latent_img, cmap='gray', vmin=0, vmax=1, aspect='auto')
        axes[3, i].set_title(f'Latent q (L={len(q_np[i])})', fontsize=10)
        axes[3, i].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Reconstructions saved to {save_path}")
    
    plt.close()


def analyze_latent_space(model, data_loader, save_path=None):
    """Analyze the learned latent space."""
    model.eval()
    
    all_q = []
    all_z = []
    all_labels = []
    
    with torch.no_grad():
        for i, (data, labels) in enumerate(data_loader):
            if i >= 20:  # Limit for speed
                break
            recon, q, z = model(data, use_straight_through=False)
            all_q.append(q)
            all_z.append(z)
            all_labels.append(labels)
    
    q_params = torch.cat(all_q, dim=0).numpy()
    z_samples = torch.cat(all_z, dim=0).numpy()
    labels = torch.cat(all_labels, dim=0).numpy()
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    # Distribution of q parameters
    axes[0, 0].hist(q_params.flatten(), bins=50, alpha=0.7, density=True, color='blue')
    axes[0, 0].set_title('Distribution of q Parameters', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('q value')
    axes[0, 0].set_ylabel('Density')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Distribution of z samples
    axes[0, 1].hist(z_samples.flatten(), bins=[0, 1], alpha=0.7, density=True, color='red')
    axes[0, 1].set_title('Distribution of z Samples', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('z value')
    axes[0, 1].set_ylabel('Density')
    axes[0, 1].set_xticks([0, 1])
    axes[0, 1].grid(True, alpha=0.3)
    
    # Mean q per latent dimension
    mean_q_per_dim = np.mean(q_params, axis=0)
    axes[0, 2].plot(mean_q_per_dim, 'o-', markersize=3)
    axes[0, 2].set_title('Mean q per Latent Dimension', fontsize=14, fontweight='bold')
    axes[0, 2].set_xlabel('Latent Dimension')
    axes[0, 2].set_ylabel('Mean q')
    axes[0, 2].grid(True, alpha=0.3)
    
    # Variance of q per latent dimension
    var_q_per_dim = np.var(q_params, axis=0)
    axes[1, 0].plot(var_q_per_dim, 'o-', markersize=3, color='green')
    axes[1, 0].set_title('Variance of q per Latent Dimension', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('Latent Dimension')
    axes[1, 0].set_ylabel('Variance')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Sparsity per dimension (fraction close to 0 or 1)
    sparsity_per_dim = np.mean((q_params < 0.1) | (q_params > 0.9), axis=0)
    axes[1, 1].plot(sparsity_per_dim, 'o-', markersize=3, color='purple')
    axes[1, 1].set_title('Sparsity per Latent Dimension', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('Latent Dimension')
    axes[1, 1].set_ylabel('Sparsity')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Correlation matrix of q parameters (sample)
    sample_size = min(1000, q_params.shape[0])
    sample_indices = np.random.choice(q_params.shape[0], sample_size, replace=False)
    q_sample = q_params[sample_indices]
    
    # Compute correlation for a subset of dimensions
    max_dims = min(20, q_params.shape[1])
    corr_matrix = np.corrcoef(q_sample[:, :max_dims].T)
    
    im = axes[1, 2].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    axes[1, 2].set_title(f'Correlation Matrix (first {max_dims} dims)', fontsize=14, fontweight='bold')
    axes[1, 2].set_xlabel('Latent Dimension')
    axes[1, 2].set_ylabel('Latent Dimension')
    plt.colorbar(im, ax=axes[1, 2])
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Latent analysis saved to {save_path}")
    
    plt.close()
    
    # Print statistics
    print("\n" + "="*50)
    print("LATENT SPACE ANALYSIS")
    print("="*50)
    print(f"Q parameters - Mean: {np.mean(q_params):.4f}, Std: {np.std(q_params):.4f}")
    print(f"Q parameters - Min: {np.min(q_params):.4f}, Max: {np.max(q_params):.4f}")
    print(f"Z samples - Fraction of 1s: {np.mean(z_samples):.4f}")
    print(f"Overall sparsity: {np.mean((q_params < 0.1) | (q_params > 0.9)):.4f}")
    print(f"Dimensions with high sparsity (>0.8): {np.sum(sparsity_per_dim > 0.8)}")
    print(f"Dimensions with low variance (<0.1): {np.sum(var_q_per_dim < 0.1)}")


def generate_comprehensive_report(checkpoint_path, results_dir='report_output'):
    """Generate comprehensive analysis report."""
    os.makedirs(results_dir, exist_ok=True)
    
    print("Loading model and generating comprehensive report...")
    
    # Load model and history
    model, history, config = load_model_and_history(checkpoint_path)
    
    # Load data
    train_loader, test_loader, _ = create_data_loaders(batch_size=128)
    
    print("Generating plots...")
    
    # Plot loss curves
    plot_loss_curves(history, os.path.join(results_dir, 'loss_curves.png'))
    
    # Plot reconstructions
    plot_reconstructions(model, test_loader, num_samples=10, 
                        save_path=os.path.join(results_dir, 'reconstructions.png'))
    
    # Analyze latent space
    analyze_latent_space(model, test_loader, 
                        save_path=os.path.join(results_dir, 'latent_analysis.png'))
    
    # Generate summary statistics
    final_epoch = len(history['epoch'])
    
    summary = {
        'model_config': config,
        'training_summary': {
            'epochs_trained': final_epoch,
            'final_train_loss': history['train_total_loss'][-1],
            'final_test_loss': history['test_total_loss'][-1],
            'final_recon_loss': history['test_recon_loss'][-1],
            'final_kl_loss': history['test_kl_loss'][-1],
            'final_entropy': history['latent_entropy_mean'][-1],
            'final_sparsity': history['latent_sparsity'][-1],
            'final_accuracy': history['reconstruction_accuracy'][-1],
            'best_test_loss': min(history['test_total_loss']),
            'best_accuracy': max(history['reconstruction_accuracy'])
        },
        'compression_analysis': {
            'latent_size': config['latent_size'],
            'input_size': config['input_size'],
            'compression_ratio': config['latent_size'] / config['input_size'],
            'entropy_reduction': 'TBD'  # Will be calculated
        }
    }
    
    # Save summary
    with open(os.path.join(results_dir, 'summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Generate text report
    with open(os.path.join(results_dir, 'report.txt'), 'w') as f:
        f.write("BERNOULLI VAE TRAINING REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("MODEL CONFIGURATION:\n")
        f.write(f"- Input size: {config['input_size']}\n")
        f.write(f"- Hidden size: {config['hidden_size']}\n")
        f.write(f"- Latent size: {config['latent_size']}\n")
        f.write(f"- Total parameters: {config['total_params']:,}\n")
        f.write(f"- Compression ratio: {config['latent_size']/config['input_size']:.3f}\n\n")
        
        f.write("TRAINING RESULTS:\n")
        f.write(f"- Epochs: {final_epoch}\n")
        f.write(f"- Final test loss: {history['test_total_loss'][-1]:.4f}\n")
        f.write(f"- Final reconstruction loss: {history['test_recon_loss'][-1]:.4f}\n")
        f.write(f"- Final KL loss: {history['test_kl_loss'][-1]:.6f}\n")
        f.write(f"- Final accuracy: {history['reconstruction_accuracy'][-1]:.4f}\n")
        f.write(f"- Best accuracy: {max(history['reconstruction_accuracy']):.4f}\n\n")
        
        f.write("COMPRESSION ANALYSIS:\n")
        f.write(f"- Final latent entropy: {history['latent_entropy_mean'][-1]:.4f} nats\n")
        f.write(f"- Final sparsity: {history['latent_sparsity'][-1]:.4f}\n")
        f.write(f"- Entropy change: {history['latent_entropy_mean'][-1] - history['latent_entropy_mean'][0]:.4f}\n")
    
    print(f"Report generated in {results_dir}/")
    return summary


if __name__ == "__main__":
    # Generate report for the best model
    checkpoint_path = input("Enter path to model checkpoint (or press Enter for default): ").strip()
    
    if not checkpoint_path:
        checkpoint_path = "results_bernoulli/best_model.pth"
    
    if os.path.exists(checkpoint_path):
        summary = generate_comprehensive_report(checkpoint_path)
        print("Report generation completed!")
    else:
        print(f"Checkpoint not found: {checkpoint_path}")
        print("Please run training first or provide correct path.")
